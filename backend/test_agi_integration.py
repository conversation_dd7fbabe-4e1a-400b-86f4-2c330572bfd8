#!/usr/bin/env python3
"""
Test AGI Layer Integration

This script tests the AGI Layer integration functionality:
1. AGI-provided Org UID usage
2. Database saving with AGI UID
3. Pipeline execution with AGI context
"""

import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_agi_integration():
    """Test AGI Layer integration functionality"""
    print("🔧 Testing AGI Layer Integration...")
    
    try:
        # Test 1: AGI Request Model
        from api.main import AGIPlantRequest
        
        # Create test AGI request with correct format
        test_request = AGIPlantRequest(
            plant_name="Sulcis Power Station",
            entity_id="AGI_ORG_IT_TEST_12345",
            extraction_levels=["organization", "plant", "unit", "transition"]
        )

        print("✅ AGI Request Model created successfully")
        print(f"   Plant Name: {test_request.plant_name}")
        print(f"   AGI Entity ID: {test_request.entity_id}")
        print(f"   Extraction Levels: {test_request.extraction_levels}")
        
        # Test 2: Database Manager AGI UID Method
        from agent.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        print("✅ Database manager imported successfully")
        
        # Check if AGI UID method exists
        if hasattr(db_manager, 'save_organization_data_with_agi_uid'):
            print("✅ AGI UID database method exists")
        else:
            print("❌ AGI UID database method missing")
            return False
        
        # Test 3: State Creation with AGI Context
        from langchain_core.messages import HumanMessage
        
        agi_state = {
            "messages": [HumanMessage(content="Sulcis Power Station")],
            "session_id": "test_agi_session",
            "search_phase": 1,
            "research_loop_count": 0,
            "web_research_result": [],
            "search_query": [],
            "sources_gathered": [],
            "org_level_complete": False,
            "continue_research": False,
            "phase_complete": False,
            "initial_search_query_count": 5,
            "max_research_loops": 3,
            "reasoning_model": "",
            "extraction_levels": ["organization", "plant", "unit", "transition"],
            # AGI Integration fields
            "agi_org_uid": "AGI_ORG_IT_TEST_12345",
            "use_agi_org_uid": True,
        }
        
        print("✅ AGI state created successfully")
        print(f"   AGI Entity ID (Org UID): {agi_state['agi_org_uid']}")
        print(f"   Use AGI UID Flag: {agi_state['use_agi_org_uid']}")
        
        # Test 4: Check Graph Import
        try:
            from agent.graph import graph
            print("✅ Graph imported successfully")
        except Exception as e:
            print(f"❌ Graph import failed: {e}")
            return False
        
        # Test 5: Test AGI UID Logic (without full execution)
        print("\n🔧 Testing AGI UID Logic...")
        
        # Simulate AGI UID check
        if agi_state.get("use_agi_org_uid") and agi_state.get("agi_org_uid"):
            org_uid = agi_state.get("agi_org_uid")
            print(f"✅ AGI UID logic working: Using AGI-provided UID: {org_uid}")
        else:
            print("❌ AGI UID logic failed")
            return False
        
        print("\n🎯 AGI INTEGRATION TEST RESULTS:")
        print("✅ AGI Request Model: Working")
        print("✅ Database AGI UID Method: Available")
        print("✅ State Management: Working")
        print("✅ Graph Import: Working")
        print("✅ AGI UID Logic: Working")
        
        return True
        
    except Exception as e:
        print(f"❌ AGI integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """Test API endpoints for AGI integration"""
    print("\n🔧 Testing API Endpoints...")
    
    try:
        # Test FastAPI app creation
        from api.main import app
        print("✅ FastAPI app imported successfully")
        
        # Check if AGI endpoint exists
        routes = [route.path for route in app.routes]
        
        if "/api/v1/extraction/agi-plant" in routes:
            print("✅ AGI plant extraction endpoint exists")
        else:
            print("❌ AGI plant extraction endpoint missing")
            return False
        
        # Check other endpoints
        expected_endpoints = [
            "/api/v1/extraction/single-plant",
            "/api/v1/extraction/entity-level",
            "/api/v1/extraction/agi-plant",
            "/api/v1/status/{job_id}",
            "/health"
        ]
        
        for endpoint in expected_endpoints:
            # Simple check (not exact match due to path parameters)
            endpoint_base = endpoint.split("{")[0]
            if any(endpoint_base in route for route in routes):
                print(f"✅ Endpoint available: {endpoint}")
            else:
                print(f"⚠️ Endpoint check unclear: {endpoint}")
        
        return True
        
    except Exception as e:
        print(f"❌ API endpoint test failed: {e}")
        return False

def test_backward_compatibility():
    """Test that existing functionality still works"""
    print("\n🔧 Testing Backward Compatibility...")
    
    try:
        # Test existing request models
        from api.main import SinglePlantRequest, EntityExtractionRequest
        
        # Test existing single plant request
        single_request = SinglePlantRequest(
            plant_name="Test Plant",
            extraction_levels=["organization", "plant", "unit"]
        )
        print("✅ Existing SinglePlantRequest still works")
        
        # Test existing entity extraction request
        entity_request = EntityExtractionRequest(
            input_plant_name="Test Plant",
            batch_size=2,
            delay_between_batches=30
        )
        print("✅ Existing EntityExtractionRequest still works")
        
        # Test database manager existing methods
        from agent.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        
        # Check existing methods still exist
        existing_methods = [
            'generate_org_uid',
            'generate_plant_uid',
            'check_plant_exists',
            'save_organization_data'
        ]
        
        for method in existing_methods:
            if hasattr(db_manager, method):
                print(f"✅ Existing method available: {method}")
            else:
                print(f"❌ Existing method missing: {method}")
                return False
        
        print("✅ Backward compatibility maintained")
        return True
        
    except Exception as e:
        print(f"❌ Backward compatibility test failed: {e}")
        return False

def main():
    """Run all AGI integration tests"""
    print("🚀 AGI LAYER INTEGRATION TESTING")
    print("=" * 50)
    
    tests = [
        ("AGI Integration Core", test_agi_integration),
        ("API Endpoints", test_api_endpoints),
        ("Backward Compatibility", test_backward_compatibility)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📋 AGI INTEGRATION TEST SUMMARY:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🚀 AGI INTEGRATION READY FOR DEPLOYMENT!")
        print("\n📋 NEXT STEPS:")
        print("1. Start FastAPI server: python3 start_api.py")
        print("2. Test AGI endpoint: POST /api/v1/extraction/agi-plant")
        print("3. Send AGI requests with plant_name + org_uid")
        print("4. Verify AGI UID is used in database and extraction")
    else:
        print("⚠️ Some tests failed - check implementation")

if __name__ == "__main__":
    main()
