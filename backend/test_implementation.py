#!/usr/bin/env python3
"""
Test Implementation Script

Tests the precision prompting and FastAPI implementation
"""

import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_precision_prompts():
    """Test precision prompting implementation"""
    print("🔧 Testing Precision Prompts...")
    
    try:
        from agent.precision_prompts import (
            get_comprehensive_plant_extraction_prompt,
            get_comprehensive_unit_extraction_prompt,
            get_country_currency
        )
        
        # Test currency mapping
        currencies = {
            'Italy': get_country_currency('Italy'),
            'Germany': get_country_currency('Germany'),
            'United States': get_country_currency('United States'),
            'Unknown': get_country_currency('Unknown Country')
        }
        
        print("✅ Currency mapping test:")
        for country, currency in currencies.items():
            print(f"   {country}: {currency}")
        
        # Test prompt generation
        plant_prompt = get_comprehensive_plant_extraction_prompt(
            "Sulcis Power Station", "Italy", "Enel S.p.A."
        )
        
        unit_prompt = get_comprehensive_unit_extraction_prompt(
            "Sulcis Power Station", "1", "Italy", {"capacity": "340 MW"}
        )
        
        print("✅ Prompt generation successful")
        print(f"   Plant prompt length: {len(plant_prompt)} characters")
        print(f"   Unit prompt length: {len(unit_prompt)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Precision prompts test failed: {e}")
        return False

def test_enhanced_discovery():
    """Test enhanced organization discovery"""
    print("\n🔧 Testing Enhanced Organization Discovery...")
    
    try:
        # Test if the enhanced prompts are in place
        from agent.quick_org_discovery import QuickOrgDiscovery
        
        discovery = QuickOrgDiscovery()
        
        # Test query generation
        queries = discovery.generate_quick_queries("Test Plant")
        
        print("✅ Enhanced discovery test:")
        print(f"   Generated {len(queries)} queries")
        for i, query in enumerate(queries, 1):
            print(f"   {i}. {query[:60]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced discovery test failed: {e}")
        return False

def test_fastapi_structure():
    """Test FastAPI structure without starting server"""
    print("\n🔧 Testing FastAPI Structure...")
    
    try:
        # Test if FastAPI files exist and are importable
        api_main_path = src_path / "api" / "main.py"
        
        if api_main_path.exists():
            print("✅ FastAPI main.py exists")
        else:
            print("❌ FastAPI main.py not found")
            return False
        
        # Test basic imports (without starting server)
        try:
            import importlib.util
            spec = importlib.util.spec_from_file_location("api.main", api_main_path)
            if spec and spec.loader:
                print("✅ FastAPI module structure valid")
            else:
                print("❌ FastAPI module structure invalid")
                return False
        except Exception as e:
            print(f"⚠️ FastAPI import test skipped: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ FastAPI structure test failed: {e}")
        return False

def test_pipeline_integration():
    """Test that existing pipeline still works"""
    print("\n🔧 Testing Pipeline Integration...")
    
    try:
        # Test existing components
        from agent.database_manager import get_database_manager
        from agent.entity_extraction_controller import EntityExtractionController
        
        # Test database
        db_manager = get_database_manager()
        print("✅ Database manager imported")
        
        # Test entity controller
        controller = EntityExtractionController("Test Plant")
        print("✅ Entity extraction controller imported")
        
        # Test graph import
        from agent.graph import graph
        print("✅ LangGraph imported")
        
        print("✅ All existing pipeline components working")
        return True
        
    except Exception as e:
        print(f"❌ Pipeline integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 TESTING PRECISION PROMPTING & FASTAPI IMPLEMENTATION")
    print("=" * 60)
    
    tests = [
        ("Precision Prompts", test_precision_prompts),
        ("Enhanced Discovery", test_enhanced_discovery),
        ("FastAPI Structure", test_fastapi_structure),
        ("Pipeline Integration", test_pipeline_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS SUMMARY:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🚀 ALL TESTS PASSED - Implementation ready!")
    else:
        print("⚠️ Some tests failed - check implementation")

if __name__ == "__main__":
    main()
