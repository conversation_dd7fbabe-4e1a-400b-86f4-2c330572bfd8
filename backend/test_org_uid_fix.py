#!/usr/bin/env python3
"""
Test org_uid Fix

This script tests that the org_uid fix resolves the "name not defined" error.
"""

import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_finalize_answer_with_bad_json():
    """Test finalize_answer with malformed JSON that would cause org_uid to be undefined"""
    print("🔧 Testing finalize_answer with malformed JSON...")
    
    try:
        from langchain_core.messages import HumanMessage, AIMessage
        from agent.graph import finalize_answer
        
        # Create state with malformed JSON response that would cause JSON parsing to fail
        malformed_response = "This is not valid JSON content without proper braces"
        
        state = {
            "messages": [
                HumanMessage(content="Test Plant"),
                AIMessage(content=malformed_response)  # This will cause JSON parsing to fail
            ],
            "session_id": "test_malformed_json",
            "search_phase": 1,  # Organization level
            "org_uid": "ORG_IN_5F0241_53093344",  # This should be in state
            "org_name": "Test Energy",
            "plant_country": "India",
        }
        
        print(f"🔧 Testing with malformed JSON response...")
        print(f"   Response: {malformed_response[:50]}...")
        print(f"   State org_uid: {state.get('org_uid')}")
        
        # This should NOT cause "name 'org_uid' is not defined" error anymore
        try:
            result = finalize_answer(state)
            print(f"✅ finalize_answer completed without org_uid error")
            return True
        except NameError as e:
            if "org_uid" in str(e):
                print(f"❌ STILL HAS ORG_UID BUG: {e}")
                return False
            else:
                print(f"❌ Different NameError: {e}")
                return False
        except Exception as e:
            print(f"⚠️ Other error (expected): {e}")
            print(f"✅ No org_uid NameError - fix is working")
            return True
            
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_finalize_answer_with_valid_json():
    """Test finalize_answer with valid JSON"""
    print("\n🔧 Testing finalize_answer with valid JSON...")
    
    try:
        from langchain_core.messages import HumanMessage, AIMessage
        from agent.graph import finalize_answer
        
        # Create state with valid JSON response
        valid_response = '''
        Here is the organization data:
        {
            "organization_name": "Test Energy",
            "country_name": "India",
            "pk": "test_pk"
        }
        '''
        
        state = {
            "messages": [
                HumanMessage(content="Test Plant"),
                AIMessage(content=valid_response)
            ],
            "session_id": "test_valid_json",
            "search_phase": 1,  # Organization level
            "org_uid": "ORG_IN_5F0241_53093344",
            "org_name": "Test Energy",
            "plant_country": "India",
            "s3_json_urls": {},
            "json_storage_complete": {},
            "json_storage_errors": []
        }
        
        print(f"🔧 Testing with valid JSON response...")
        print(f"   State org_uid: {state.get('org_uid')}")
        
        # This should work correctly and not cause any org_uid errors
        try:
            result = finalize_answer(state)
            print(f"✅ finalize_answer completed successfully with valid JSON")
            return True
        except NameError as e:
            if "org_uid" in str(e):
                print(f"❌ STILL HAS ORG_UID BUG: {e}")
                return False
            else:
                print(f"❌ Different NameError: {e}")
                return False
        except Exception as e:
            print(f"⚠️ Other error (may be expected): {e}")
            print(f"✅ No org_uid NameError - fix is working")
            return True
            
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run org_uid fix tests"""
    print("🚀 ORG_UID FIX TESTING")
    print("=" * 50)
    
    tests = [
        ("Malformed JSON (org_uid bug scenario)", test_finalize_answer_with_bad_json),
        ("Valid JSON (normal scenario)", test_finalize_answer_with_valid_json)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📋 ORG_UID FIX TEST SUMMARY:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 ORG_UID BUG IS FIXED!")
        print("\n📋 WHAT WAS FIXED:")
        print("- org_uid variable is now initialized outside conditional block")
        print("- No more 'name org_uid is not defined' errors")
        print("- Entity extraction should work correctly now")
        print("\n🚀 READY TO TEST WITH REAL AGI REQUEST!")
    else:
        print("❌ org_uid bug still exists - check the failing tests")

if __name__ == "__main__":
    main()
