#!/usr/bin/env python3
"""
AGI Layer Integration Example

This script shows the exact format the AGI Layer should use to send requests
to the Technical Pipeline API.
"""

import requests
import json
import time

def send_agi_request(plant_name: str, entity_id: str, api_base_url: str = "http://localhost:8000"):
    """
    Send AGI request to Technical Pipeline
    
    Args:
        plant_name: Name of the power plant
        entity_id: Organization UID from AGI Layer
        api_base_url: Base URL of the Technical Pipeline API
    """
    
    # EXACT FORMAT AGI LAYER SHOULD USE
    agi_request = {
        "plant_name": plant_name,
        "entity_id": entity_id
    }
    
    print(f"🔧 Sending AGI request to Technical Pipeline:")
    print(f"   Plant Name: {plant_name}")
    print(f"   Entity ID: {entity_id}")
    print(f"   Request Format: {json.dumps(agi_request, indent=2)}")
    
    try:
        # Send POST request to AGI endpoint
        response = requests.post(
            f"{api_base_url}/api/v1/extraction/agi-plant",
            json=agi_request,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Request successful!")
            print(f"   Job ID: {result.get('job_id')}")
            print(f"   Status: {result.get('status')}")
            print(f"   Status URL: {api_base_url}{result.get('status_url')}")
            
            return result.get('job_id')
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return None

def monitor_job_status(job_id: str, api_base_url: str = "http://localhost:8000"):
    """
    Monitor the status of an AGI extraction job
    
    Args:
        job_id: Job ID returned from the AGI request
        api_base_url: Base URL of the Technical Pipeline API
    """
    
    if not job_id:
        print("❌ No job ID to monitor")
        return
    
    print(f"\n🔍 Monitoring job status: {job_id}")
    
    max_attempts = 30  # Monitor for up to 15 minutes (30 * 30 seconds)
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(f"{api_base_url}/api/v1/status/{job_id}")
            
            if response.status_code == 200:
                status_data = response.json()
                status = status_data.get('status')
                phase = status_data.get('details', {}).get('phase', 'unknown')
                
                print(f"   Status: {status} | Phase: {phase}")
                
                if status == 'completed':
                    print(f"✅ Job completed successfully!")
                    print(f"   Results: {json.dumps(status_data.get('results', {}), indent=2)}")
                    break
                elif status == 'failed':
                    print(f"❌ Job failed!")
                    print(f"   Error: {status_data.get('error', 'Unknown error')}")
                    break
                elif status in ['running', 'started']:
                    print(f"⏳ Job in progress...")
                else:
                    print(f"⚠️ Unknown status: {status}")
                
            else:
                print(f"❌ Status check failed: {response.status_code}")
                break
                
        except Exception as e:
            print(f"❌ Status check error: {e}")
            break
        
        attempt += 1
        time.sleep(30)  # Wait 30 seconds between checks
    
    if attempt >= max_attempts:
        print(f"⏰ Monitoring timeout after {max_attempts * 30} seconds")

def main():
    """
    Example usage for AGI Layer integration
    """
    print("🚀 AGI LAYER INTEGRATION EXAMPLE")
    print("=" * 50)
    
    # Example 1: Italian Power Plant
    print("\n📋 EXAMPLE 1: Italian Power Plant")
    job_id_1 = send_agi_request(
        plant_name="Sulcis Power Station",
        entity_id="AGI_ORG_IT_ENEL_12345"
    )
    
    # Example 2: Indian Power Plant
    print("\n📋 EXAMPLE 2: Indian Power Plant")
    job_id_2 = send_agi_request(
        plant_name="Taichung Power Plant",
        entity_id="AGI_ORG_TW_TAIPOWER_67890"
    )
    
    # Example 3: US Power Plant
    print("\n📋 EXAMPLE 3: US Power Plant")
    job_id_3 = send_agi_request(
        plant_name="Cardinal Power Plant",
        entity_id="AGI_ORG_US_AEP_54321"
    )
    
    print("\n" + "=" * 50)
    print("📋 AGI REQUEST FORMAT SUMMARY:")
    print("""
    The AGI Layer should send requests in this exact format:
    
    POST /api/v1/extraction/agi-plant
    Content-Type: application/json
    
    {
        "plant_name": "Power Plant Name",
        "entity_id": "AGI_Generated_Org_UID"
    }
    
    The Technical Pipeline will:
    1. Use the provided entity_id as the organization UID
    2. Perform quick organization discovery
    3. Generate plant UIDs for each discovered plant
    4. Execute 3-level extraction (org → plant → unit → transition)
    5. Save results to S3 with hierarchical structure
    6. Send completion message to SQS queue
    """)
    
    # Monitor one of the jobs as example
    if job_id_1:
        monitor_job_status(job_id_1)

if __name__ == "__main__":
    main()
