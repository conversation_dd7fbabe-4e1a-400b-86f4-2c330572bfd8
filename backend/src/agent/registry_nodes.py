"""
Plant Registry LangGraph Nodes

This module contains the new LangGraph nodes for plant registry functionality:
1. Plant registry check
2. Quick organization discovery
3. UID generation
4. Financial pipeline trigger
5. Database population
"""

import os
from typing import Dict, Any
from langchain_core.runnables import RunnableConfig
from langgraph.types import Send

from agent.state import OverallState
from agent.database_manager import get_database_manager
from agent.quick_org_discovery import perform_quick_org_discovery
from agent.utils import get_research_topic
from agent.configuration import Configuration

# Import your existing web search functionality
from google.genai import Client
import os

def get_web_search_function():
    """
    Get the web search function using your existing Google Search API implementation
    """
    genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))
    
    def perform_web_search(query: str):
        """
        Perform web search using Google Search API via Gemini
        
        Args:
            query: Search query string
            
        Returns:
            List of search results
        """
        try:
            # Use your existing web search approach with Gemini + Google Search API
            from agent.prompts import web_searcher_instructions, get_current_date
            from agent.configuration import Configuration
            from langchain_core.runnables import RunnableConfig
            
            configurable = Configuration.from_runnable_config(RunnableConfig(configurable={}))
            current_date = get_current_date()
            
            # Format the search prompt using your existing template
            formatted_prompt = web_searcher_instructions.format(
                current_date=current_date,
                research_topic=query  # Use the query as research topic
            )
            
            # Use Google Search API through genai client (your existing approach)
            response = genai_client.models.generate_content(
                model=configurable.web_searcher_model,
                contents=formatted_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0,
                    "max_output_tokens": 2048,
                    "top_k": 40,
                    "top_p": 0.95,
                }
            )
            
            # Extract search results from response
            if response and hasattr(response, 'text'):
                # Parse the response to extract search results
                # This is a simplified version - you may need to adapt based on your exact response format
                results = []
                content = response.text
                
                # Create a single comprehensive result from the search
                results.append({
                    "title": f"Search results for: {query}",
                    "content": content,
                    "url": "https://search.google.com"
                })
                
                return results
            else:
                return []
                
        except Exception as e:
            print(f"⚠️ Web search failed for query '{query}': {e}")
            return []
    
    return perform_web_search

def check_plant_registry(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to check if plant exists in registry database
    
    This is the first node in the enhanced pipeline that checks if we already
    have organization information for the requested plant.
    
    Args:
        state: Current graph state
        
    Returns:
        State update with registry check results
    """
    session_id = state.get("session_id", "unknown")
    messages = state.get("messages", [])
    
    # Reset plant ID counter at the start of each session
    from agent.graph import reset_plant_id_counter
    reset_plant_id_counter()
    print(f"[Session {session_id}] 🔄 Reset plant_id counter to start from 1")
    
    # Extract plant name from messages
    plant_name = get_research_topic(messages) if messages else ""
    
    print(f"[Session {session_id}] 🔍 PLANT REGISTRY CHECK")
    print(f"[Session {session_id}] Checking plant: {plant_name}")
    
    if not plant_name:
        print(f"[Session {session_id}] ⚠️ No plant name found in messages")
        return {
            "plant_exists_in_db": False,
            "registry_check_complete": True,
            "registry_error": "No plant name provided"
        }
    
    try:
        # Check database for existing plant
        db_manager = get_database_manager()
        existing_plant = db_manager.check_plant_exists(plant_name)
        
        if existing_plant:
            print(f"[Session {session_id}] ✅ Plant found in registry!")
            print(f"[Session {session_id}]    Organization: {existing_plant['org_name']}")
            print(f"[Session {session_id}]    UID: {existing_plant['org_uid']}")
            print(f"[Session {session_id}]    Country: {existing_plant['country']}")
            
            return {
                "plant_exists_in_db": True,
                "existing_plant_info": existing_plant,
                "org_uid": existing_plant["org_uid"],
                "org_name": existing_plant["org_name"],
                "plant_country": existing_plant["country"],
                "registry_check_complete": True,
                "registry_error": ""
            }
        else:
            print(f"[Session {session_id}] ℹ️ Plant not found in registry - will discover")
            return {
                "plant_exists_in_db": False,
                "registry_check_complete": True,
                "registry_error": ""
            }
            
    except Exception as e:
        error_msg = f"Registry check failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "plant_exists_in_db": False,
            "registry_check_complete": True,
            "registry_error": error_msg
        }

def quick_org_discovery_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node for quick organization discovery
    
    This node performs lightweight organization discovery to get basic
    organization information and plant list without full extraction.
    
    Args:
        state: Current graph state
        
    Returns:
        State update with organization discovery results
    """
    session_id = state.get("session_id", "unknown")
    messages = state.get("messages", [])
    
    # Extract plant name from messages
    plant_name = get_research_topic(messages) if messages else ""
    
    print(f"[Session {session_id}] 🚀 QUICK ORGANIZATION DISCOVERY")
    print(f"[Session {session_id}] Discovering organization for: {plant_name}")
    
    if not plant_name:
        return {
            "quick_discovery_complete": False,
            "discovery_error": "No plant name provided"
        }
    
    try:
        # Get web search function (adapt to your implementation)
        web_search_fn = get_web_search_function()
        
        # Perform quick discovery
        org_info = perform_quick_org_discovery(plant_name, web_search_fn)
        
        print(f"[Session {session_id}] ✅ Quick discovery complete")
        print(f"[Session {session_id}]    Organization: {org_info['org_name']}")
        print(f"[Session {session_id}]    Country: {org_info['country']}")
        print(f"[Session {session_id}]    Plants discovered: {len(org_info['plants'])}")
        
        return {
            "quick_discovery_complete": True,
            "discovered_org_info": org_info,
            "org_name": org_info["org_name"],
            "plant_country": org_info["country"],
            "discovered_plants": org_info["plants"],
            "discovery_error": ""
        }
        
    except Exception as e:
        error_msg = f"Quick discovery failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "quick_discovery_complete": False,
            "discovery_error": error_msg
        }

def generate_uid_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to generate organization UID
    
    This node generates a unique organization UID based on the organization
    name and country information.
    
    Args:
        state: Current graph state
        
    Returns:
        State update with generated UID
    """
    session_id = state.get("session_id", "unknown")
    org_name = state.get("org_name", "")
    country = state.get("plant_country", "")

    print(f"[Session {session_id}] 🆔🆔🆔 GENERATE UID NODE CALLED! 🆔🆔🆔")
    print(f"[Session {session_id}] 🔍 DEBUG: State keys: {list(state.keys())}")
    print(f"[Session {session_id}] 🔑 UID GENERATION")
    print(f"[Session {session_id}] Organization: {org_name}")
    print(f"[Session {session_id}] Country: {country}")
    
    if not org_name or not country:
        error_msg = "Missing organization name or country for UID generation"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "uid_generation_complete": False,
            "uid_error": error_msg
        }
    
    try:
        # Check if we already have a UID from existing plant info
        existing_uid = state.get("org_uid", "")
        
        if existing_uid:
            # Use existing UID (plant was found in database)
            print(f"[Session {session_id}] ✅ Using existing UID: {existing_uid}")
            org_uid = existing_uid
        else:
            # Generate new UID (new organization discovery)
            db_manager = get_database_manager()
            org_uid = db_manager.generate_org_uid(org_name, country)
            print(f"[Session {session_id}] ✅ New UID generated: {org_uid}")
        
        return {
            "org_uid": org_uid,
            "uid_generation_complete": True,
            "uid_error": ""
        }
        
    except Exception as e:
        error_msg = f"UID generation failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "uid_generation_complete": False,
            "uid_error": error_msg
        }

# Removed duplicate function - using the newer SQS implementation below

def populate_database_async_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to populate database with discovered plants
    
    This node runs in the background to save all discovered plants
    to the database for future lookups.
    
    Args:
        state: Current graph state
        
    Returns:
        State update with database population results
    """
    session_id = state.get("session_id", "unknown")
    org_name = state.get("org_name", "")
    country = state.get("plant_country", "")
    org_uid = state.get("org_uid", "")
    discovered_plants = state.get("discovered_plants", [])
    plant_name = get_research_topic(state.get("messages", []))
    
    print(f"[Session {session_id}] 💾 DATABASE POPULATION")
    print(f"[Session {session_id}] Saving {len(discovered_plants)} plants")
    
    if not all([org_name, country, org_uid, discovered_plants]):
        error_msg = "Missing required information for database population"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "database_population_complete": False,
            "database_error": error_msg
        }
    
    try:
        # Save plants to database
        db_manager = get_database_manager()
        success = db_manager.save_organization_plants(
            org_name=org_name,
            country=country,
            plants_list=discovered_plants,
            org_uid=org_uid,
            discovery_session_id=session_id,
            discovered_from_plant=plant_name
        )
        
        if success:
            print(f"[Session {session_id}] ✅ Database population complete")

            # CRITICAL FIX: Get plant_uid for the input plant and store in state
            plant_uid = None
            try:
                # First try exact match
                existing_plant = db_manager.check_plant_exists(plant_name)
                if existing_plant and existing_plant.get("plant_uid"):
                    plant_uid = existing_plant["plant_uid"]
                    print(f"[Session {session_id}] 🔑 Retrieved plant_uid for state: {plant_uid}")
                else:
                    # If not found, try to find any plant from the same organization
                    print(f"[Session {session_id}] ⚠️ No exact match for '{plant_name}', trying organization plants...")
                    org_plants = db_manager.get_plants_by_organization_uid(org_uid)
                    if org_plants:
                        # Use the first plant's UID as fallback
                        plant_uid = org_plants[0]["plant_uid"]  # Fixed: access as dict key
                        print(f"[Session {session_id}] 🔑 Using fallback plant_uid from org: {plant_uid}")
                        print(f"[Session {session_id}] 📋 Available plants: {[p['plant_name'] for p in org_plants]}")  # Fixed: access as dict key
                    else:
                        print(f"[Session {session_id}] ⚠️ No plants found for organization {org_uid}")
            except Exception as e:
                print(f"[Session {session_id}] ❌ Error retrieving plant_uid: {e}")

            # SIMPLIFIED: Always complete database population (multi-plant extraction removed)
            print(f"[Session {session_id}] 🏭 Plants saved ({len(discovered_plants)} plants)")
            return {
                "database_population_complete": True,
                "plants_saved_count": len(discovered_plants),
                "database_error": "",
                "org_uid": org_uid,  # CRITICAL: Store org_uid in state
                "plant_uid": plant_uid  # NEW: Store plant_uid in state
            }
        else:
            raise Exception("Database save operation failed")
            
    except Exception as e:
        error_msg = f"Database population failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "database_population_complete": False,
            "database_error": error_msg
        }

def route_after_database_population(state: OverallState) -> str:
    """
    Route after database population - decide whether to run entity extraction or single-plant flow

    Args:
        state: Current graph state

    Returns:
        Next node name
    """
    session_id = state.get("session_id", "unknown")
    plants_saved = state.get("plants_saved_count", 0)
    discovered_plants = state.get("discovered_plants", [])

    print(f"[Session {session_id}] 🔀 ROUTING AFTER DATABASE POPULATION")
    print(f"[Session {session_id}] 🔍 Plants saved: {plants_saved}")
    print(f"[Session {session_id}] 🔍 Discovered plants: {len(discovered_plants)}")

    # Decision: Entity extraction vs Single plant extraction
    if len(discovered_plants) > 1:
        print(f"[Session {session_id}] 🏭 Multiple plants detected ({len(discovered_plants)} plants)")
        print(f"[Session {session_id}] ➡️  ROUTING TO: entity_extraction_trigger (start entity-level extraction)")
        return "entity_extraction_trigger"
    else:
        print(f"[Session {session_id}] 🏭 Single plant detected")
        print(f"[Session {session_id}] ➡️  ROUTING TO: spawn_parallel_processing_with_uid (start 3-level + image extraction)")
        return "spawn_parallel_processing_with_uid"

def setup_multi_plant_counter(state: OverallState) -> Dict[str, Any]:
    """
    SIMPLE APPROACH: Setup counter for multi-plant processing

    Instead of complex multi-plant extractor, just setup a counter
    and start processing the first plant using normal 3-level extraction
    """
    session_id = state.get("session_id", "unknown")
    org_uid = state.get("org_uid", "")

    print(f"[Session {session_id}] 🔢 SETTING UP MULTI-PLANT COUNTER")

    try:
        # Get all plants for this organization from database
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plants = db_manager.get_plants_by_organization_uid(org_uid)

        if not plants:
            print(f"[Session {session_id}] ❌ No plants found in database for org_uid: {org_uid}")
            return {
                "multi_plant_error": "No plants found in database",
                "plant_counter": 0,
                "total_plants": 0
            }

        # Setup counter variables
        total_plants = len(plants)
        current_counter = 1
        first_plant = plants[0]

        print(f"[Session {session_id}] 📊 Multi-plant setup:")
        print(f"[Session {session_id}]   - Total plants: {total_plants}")
        print(f"[Session {session_id}]   - Starting with plant {current_counter}/{total_plants}: {first_plant.plant_name}")

        # Update state to start processing first plant
        from langchain_core.messages import HumanMessage

        return {
            "plant_counter": current_counter,
            "total_plants": total_plants,
            "multi_plant_mode": True,
            "messages": [HumanMessage(content=first_plant.plant_name)],  # Start with first plant
            "current_plant_name": first_plant.plant_name,
            "multi_plant_setup_complete": True
        }

    except Exception as e:
        error_msg = f"Multi-plant counter setup failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "multi_plant_error": error_msg,
            "plant_counter": 0,
            "total_plants": 0
        }

# Helper functions

# FINANCIAL PIPELINE INTEGRATION COMPLETELY REMOVED
# AGI Layer now handles coordination between technical and financial pipelines
# No direct communication needed from technical pipeline to financial pipeline

# Routing functions for LangGraph

def route_after_registry_check(state: OverallState) -> str:
    """
    Route after plant registry check
    
    Args:
        state: Current graph state
        
    Returns:
        Next node name
    """
    if state.get("plant_exists_in_db", False):
        return "generate_uid"  # Skip discovery, go straight to UID
    else:
        return "quick_org_discovery"  # Need to discover organization

def entity_extraction_trigger_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to trigger entity-level extraction

    This node starts the entity-level extraction process for all plants
    in the organization after database population is complete.

    Args:
        state: Current graph state

    Returns:
        State update with entity extraction trigger
    """
    session_id = state.get("session_id", "unknown")
    org_name = state.get("org_name", "")
    discovered_plants = state.get("discovered_plants", [])

    print(f"[Session {session_id}] 🚀 ENTITY EXTRACTION TRIGGER")
    print(f"[Session {session_id}] Organization: {org_name}")
    print(f"[Session {session_id}] Plants to process: {len(discovered_plants)}")

    if len(discovered_plants) <= 1:
        print(f"[Session {session_id}] ⚠️ Only {len(discovered_plants)} plant(s) found - skipping entity extraction")
        return {
            "entity_extraction_triggered": False,
            "entity_extraction_reason": "Insufficient plants for entity extraction"
        }

    try:
        # Import and initialize entity extraction controller
        from agent.entity_extraction_controller import EntityExtractionController

        # Get the input plant name that triggered this discovery
        messages = state.get("messages", [])
        input_plant_name = ""
        if messages:
            from agent.utils import get_research_topic
            input_plant_name = get_research_topic(messages)

        if not input_plant_name:
            print(f"[Session {session_id}] ❌ Could not determine input plant name")
            return {
                "entity_extraction_triggered": False,
                "entity_extraction_error": "Could not determine input plant name"
            }

        print(f"[Session {session_id}] 🎯 Triggering entity extraction for: {input_plant_name}")
        print(f"[Session {session_id}] 📋 Will process {len(discovered_plants)} plants")

        # Create controller and start extraction (async)
        controller = EntityExtractionController(input_plant_name)

        # For now, we'll trigger it and let it run in background
        # In production, you might want to use a task queue like Celery
        import asyncio
        import threading

        def run_entity_extraction():
            """Run entity extraction in background thread"""
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(controller.execute_entity_extraction())
                print(f"[Session {session_id}] ✅ Entity extraction completed: {result.get('success', False)}")
            except Exception as e:
                print(f"[Session {session_id}] ❌ Entity extraction failed: {e}")
            finally:
                loop.close()

        # Start entity extraction in background
        extraction_thread = threading.Thread(target=run_entity_extraction, daemon=True)
        extraction_thread.start()

        return {
            "entity_extraction_triggered": True,
            "entity_extraction_status": "started_in_background",
            "entity_plants_count": len(discovered_plants)
        }

    except Exception as e:
        error_msg = f"Entity extraction trigger failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "entity_extraction_triggered": False,
            "entity_extraction_error": error_msg
        }

def route_after_uid_generation(state: OverallState) -> str:
    """
    Route after UID generation - decide whether to populate database or run multi-plant extraction

    Args:
        state: Current graph state

    Returns:
        Next node name
    """
    session_id = state.get("session_id", "unknown")
    discovered_plants = state.get("discovered_plants", [])
    org_uid = state.get("org_uid", "")

    print(f"[Session {session_id}] 🔀 ROUTING AFTER UID GENERATION")
    print(f"[Session {session_id}] 🔍 Discovered plants: {len(discovered_plants)} plants")
    print(f"[Session {session_id}] 🔍 Organization UID: {org_uid}")
    print(f"[Session {session_id}] 🔍 State keys: {list(state.keys())}")

    # CRITICAL FIX: Check discovered plants FIRST, then existing plants in database

    # Case 1: Multiple plants discovered in current session (need to save them first)
    if discovered_plants and len(discovered_plants) > 1:
        print(f"[Session {session_id}] 🏭 Multiple plants discovered ({len(discovered_plants)} plants)")
        print(f"[Session {session_id}] 📝 Need to save plants to database first")
        print(f"[Session {session_id}] ➡️  ROUTING TO: populate_database_async (then will trigger multi-plant)")
        return "populate_database_async"

    # Case 2: Check if organization already has multiple plants in database (existing multi-plant scenario)
    if org_uid:
        try:
            # Use database manager directly instead of deleted multi_plant_extraction
            db_manager = get_database_manager()
            existing_plants = db_manager.get_plants_by_organization_uid(org_uid)

            if len(existing_plants) >= 1:  # FIXED: >= 1 instead of > 1
                print(f"[Session {session_id}] 🏭 Found {len(existing_plants)} plants already in database")
                print(f"[Session {session_id}] ➡️  ROUTING TO: spawn_parallel_processing_with_uid (start 3-level + image extraction)")
                return "spawn_parallel_processing_with_uid"
            else:
                print(f"[Session {session_id}] 🏭 No plants found in database for org_uid: {org_uid}")
        except Exception as e:
            print(f"[Session {session_id}] ⚠️ Error checking existing plants: {e}")

    # Case 3: Single plant discovered (save to database)
    if discovered_plants and len(discovered_plants) == 1:
        print(f"[Session {session_id}] 🏭 Single plant discovered")
        print(f"[Session {session_id}] ➡️  ROUTING TO: populate_database_async")
        return "populate_database_async"

    # Case 4: No new plants discovered, plant already existed
    print(f"[Session {session_id}] 🏭 No new plants discovered, plant already existed")
    print(f"[Session {session_id}] ➡️  ROUTING TO: spawn_parallel_processing_with_uid (start 3-level + image extraction)")
    return "spawn_parallel_processing_with_uid"

def check_for_next_plant(state: OverallState) -> str:
    """
    SIMPLE APPROACH: Check if there are more plants to process after completing one

    This function is called after completing 3-level extraction for one plant.
    If there are more plants, it sets up the next plant and routes back to org_generate_query.
    If all plants are done, it routes to trigger_financial_pipeline.
    """
    session_id = state.get("session_id", "unknown")
    plant_counter = state.get("plant_counter", 1)
    total_plants = state.get("total_plants", 1)
    multi_plant_mode = state.get("multi_plant_mode", False)
    org_uid = state.get("org_uid", "")

    print(f"[Session {session_id}] 🔢 CHECKING FOR NEXT PLANT")
    print(f"[Session {session_id}]   - Current: {plant_counter}/{total_plants}")
    print(f"[Session {session_id}]   - Multi-plant mode: {multi_plant_mode}")

    # If not in multi-plant mode, start parallel processing (3-level + image extraction)
    if not multi_plant_mode:
        print(f"[Session {session_id}] ➡️  Not in multi-plant mode → spawn_parallel_processing_with_uid (start 3-level + image extraction)")
        return "spawn_parallel_processing_with_uid"

    # If we've processed all plants, go to financial pipeline
    if plant_counter >= total_plants:
        print(f"[Session {session_id}] ✅ All plants processed ({plant_counter}/{total_plants}) → trigger_financial_pipeline")
        return "trigger_financial_pipeline"

    # More plants to process - get next plant
    try:
        db_manager = get_database_manager()
        plants = db_manager.get_plants_by_organization_uid(org_uid)

        if plant_counter < len(plants):
            next_plant = plants[plant_counter]  # 0-indexed, so plant_counter is the next index
            next_counter = plant_counter + 1

            print(f"[Session {session_id}] ➡️  Processing next plant {next_counter}/{total_plants}: {next_plant.plant_name}")

            # Update state for next plant
            from langchain_core.messages import HumanMessage
            state["plant_counter"] = next_counter
            state["current_plant_name"] = next_plant.plant_name
            state["messages"] = [HumanMessage(content=next_plant.plant_name)]

            # Route back to start 3-level extraction for next plant
            return "org_generate_query"
        else:
            print(f"[Session {session_id}] ✅ No more plants to process → trigger_financial_pipeline")
            return "trigger_financial_pipeline"

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error getting next plant: {e}")
        return "trigger_financial_pipeline"