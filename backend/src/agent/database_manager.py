"""
Power Plant Registry Database Manager

This module handles the database operations for the power plant registry system.
Supports both SQLite (development) and PostgreSQL (production) through SQLAlchemy.
"""

import os
import hashlib
import time
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, Index, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
import enum

# Database configuration
Base = declarative_base()

class PlantStatus(enum.Enum):
    OPERATIONAL = "operational"
    UNDER_CONSTRUCTION = "under_construction"
    DECOMMISSIONED = "decommissioned"
    RETIRED = "retired"
    UNKNOWN = "unknown"

class DiscoveryStatus(enum.Enum):
    PARTIAL = "partial"  # Only basic org info discovered
    COMPLETE = "complete"  # Full plant list discovered
    FAILED = "failed"  # Discovery failed

class EntityJobStatus(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"

class PlantExtractionStatusEnum(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class ExtractionPhase(enum.Enum):
    NOT_STARTED = "not_started"
    ORGANIZATION = "organization"
    PLANT = "plant"
    UNIT_EXTRACTION = "unit_extraction"
    TRANSITION_PLAN = "transition_plan"
    COMPLETED = "completed"

class PowerPlantRegistry(Base):
    """
    Database model for power plant registry
    
    This table stores information about power plants and their parent organizations.
    Each organization gets a unique UID, and all plants under that organization
    share the same org_uid.
    """
    __tablename__ = 'power_plants_registry'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    org_name = Column(String(255), nullable=False, index=True)
    plant_name = Column(String(255), nullable=False)
    country = Column(String(100), nullable=False)
    org_uid = Column(String(50), nullable=False, index=True)
    plant_uid = Column(String(50), nullable=True, index=True)  # NEW: Plant-specific UID
    plant_status = Column(Enum(PlantStatus), default=PlantStatus.OPERATIONAL)
    discovery_status = Column(Enum(DiscoveryStatus), default=DiscoveryStatus.PARTIAL)
    discovery_session_id = Column(String(50), nullable=True)
    discovered_from_plant = Column(String(255), nullable=True)  # Which plant triggered the discovery
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_plant_country', 'plant_name', 'country'),
        Index('idx_org_uid', 'org_uid'),
        Index('idx_org_name', 'org_name'),
    )

class EntityExtractionJob(Base):
    """
    Database model for entity-level extraction job tracking

    This table tracks multi-plant extraction jobs for entire organizations.
    Each job represents processing all plants for a single organization.
    """
    __tablename__ = 'entity_extraction_jobs'

    job_id = Column(String(50), primary_key=True)
    org_name = Column(String(255), nullable=False, index=True)
    org_uid = Column(String(50), nullable=False, index=True)
    input_plant_name = Column(String(255), nullable=False)  # Plant that triggered the job
    total_plants = Column(Integer, default=0)
    completed_plants = Column(Integer, default=0)
    failed_plants = Column(Integer, default=0)
    status = Column(Enum(EntityJobStatus), default=EntityJobStatus.PENDING)
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    error_log = Column(String(2000), nullable=True)
    progress_data = Column(String(5000), nullable=True)  # JSON string for detailed progress

    # Indexes for performance
    __table_args__ = (
        Index('idx_job_org_uid', 'org_uid'),
        Index('idx_job_status', 'status'),
        Index('idx_job_started', 'started_at'),
    )

class PlantExtractionStatus(Base):
    """
    Database model for individual plant extraction status within entity jobs

    This table tracks the extraction status of each plant within an entity-level job.
    """
    __tablename__ = 'plant_extraction_status'

    id = Column(Integer, primary_key=True, autoincrement=True)
    job_id = Column(String(50), nullable=False, index=True)
    plant_uid = Column(String(50), nullable=False, index=True)
    plant_name = Column(String(255), nullable=False)
    extraction_status = Column(Enum(PlantExtractionStatusEnum), default=PlantExtractionStatusEnum.PENDING)
    extraction_phase = Column(Enum(ExtractionPhase), default=ExtractionPhase.NOT_STARTED)
    error_message = Column(String(1000), nullable=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    s3_urls = Column(String(2000), nullable=True)  # JSON string of S3 URLs
    retry_count = Column(Integer, default=0)

    # Indexes for performance
    __table_args__ = (
        Index('idx_plant_job_id', 'job_id'),
        Index('idx_plant_status', 'extraction_status'),
        Index('idx_plant_phase', 'extraction_phase'),
    )

class DatabaseManager:
    """
    Database manager for power plant registry operations
    
    Handles database connections, CRUD operations, and UID generation.
    Supports both SQLite (development) and PostgreSQL (production).
    """
    
    def __init__(self, database_url: Optional[str] = None):
        """
        Initialize database manager
        
        Args:
            database_url: Database connection URL. If None, uses environment variable
                         or defaults to SQLite for development
        """
        if database_url is None:
            database_url = self._get_database_url()
        
        self.database_url = database_url
        self.engine = create_engine(
            database_url,
            echo=False,  # Set to True for SQL debugging
            pool_pre_ping=True,  # Verify connections before use
        )
        
        # Configure connection pooling for PostgreSQL
        if 'postgresql' in database_url:
            self.engine = create_engine(
                database_url,
                echo=False,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
            )
        
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        self.db_type = "sqlite" if "sqlite" in database_url else "postgresql"
        
        # Create tables if they don't exist
        self.create_tables()
    
    def _get_database_url(self) -> str:
        """
        Get database URL based on environment
        
        Returns:
            Database connection URL
        """
        env = os.getenv("ENVIRONMENT", "development")
        
        if env == "development":
            # SQLite for development
            db_path = os.path.join(os.path.dirname(__file__), "powerplant_registry.db")
            return f"sqlite:///{db_path}"
        elif env == "testing":
            # In-memory SQLite for testing
            return "sqlite:///:memory:"
        else:
            # PostgreSQL for production
            db_url = os.getenv("DATABASE_URL")
            if not db_url:
                raise ValueError("DATABASE_URL environment variable is required for production")
            return db_url
    
    def create_tables(self):
        """Create database tables if they don't exist"""
        try:
            Base.metadata.create_all(bind=self.engine)
            print(f"✅ Database tables created/verified ({self.db_type})")
        except Exception as e:
            print(f"❌ Error creating database tables: {e}")
            raise
    
    def get_session(self) -> Session:
        """Get database session"""
        return self.SessionLocal()
    
    def generate_org_uid(self, org_name: str, country: str) -> str:
        """
        Generate unique organization UID
        
        Format: ORG_{COUNTRY_CODE}_{ORG_HASH}_{TIMESTAMP}
        Example: ORG_IN_A7B2C9_20241201
        
        Args:
            org_name: Organization name
            country: Country name
            
        Returns:
            Unique organization UID
        """
        # Get country code (first 2 letters, uppercase)
        country_code = country[:2].upper()
        
        # Create hash from organization name
        org_hash = hashlib.sha256(org_name.lower().encode()).hexdigest()[:6].upper()
        
        # Add timestamp for uniqueness
        timestamp = str(int(time.time()))[-8:]  # Last 8 digits
        
        org_uid = f"ORG_{country_code}_{org_hash}_{timestamp}"
        
        # Ensure uniqueness (very rare collision case)
        session = self.get_session()
        try:
            existing = session.query(PowerPlantRegistry).filter_by(org_uid=org_uid).first()
            if existing:
                # Add random suffix if collision occurs
                import random
                suffix = str(random.randint(1000, 9999))
                org_uid = f"{org_uid}_{suffix}"
            
            return org_uid
        finally:
            session.close()

    def _normalize_plant_status(self, raw_status: str) -> PlantStatus:
        """
        Normalize plant status string to PlantStatus enum

        Args:
            raw_status: Raw status string from plant data

        Returns:
            PlantStatus enum value
        """
        if not raw_status:
            return PlantStatus.OPERATIONAL

        # Normalize to lowercase and strip whitespace
        normalized = raw_status.lower().strip()

        # Status mapping for common variations
        status_mapping = {
            "operational": PlantStatus.OPERATIONAL,
            "active": PlantStatus.OPERATIONAL,
            "running": PlantStatus.OPERATIONAL,
            "under_construction": PlantStatus.UNDER_CONSTRUCTION,
            "under construction": PlantStatus.UNDER_CONSTRUCTION,
            "construction": PlantStatus.UNDER_CONSTRUCTION,
            "decommissioned": PlantStatus.DECOMMISSIONED,
            "retired": PlantStatus.RETIRED,
            "closed": PlantStatus.RETIRED,
            "unknown": PlantStatus.UNKNOWN,
            "": PlantStatus.UNKNOWN
        }

        result = status_mapping.get(normalized, PlantStatus.OPERATIONAL)
        print(f"   🔧 Status normalization: '{raw_status}' → '{result.value}'")
        return result

    def generate_plant_uid(self, plant_name: str, org_uid: str) -> str:
        """
        Generate unique plant UID

        Format: PLT_{ORG_HASH}_{PLANT_HASH}_{TIMESTAMP}
        Example: PLT_A7B2C9_D4E5F6_20241201

        Args:
            plant_name: Plant name
            org_uid: Organization UID (for consistency)

        Returns:
            Unique plant UID
        """
        # Extract org hash from org_uid (e.g., ORG_IN_A7B2C9_52657472 -> A7B2C9)
        org_parts = org_uid.split('_')
        org_hash = org_parts[2] if len(org_parts) >= 3 else "UNKNOWN"

        # Create hash from plant name
        plant_hash = hashlib.sha256(plant_name.lower().encode()).hexdigest()[:6].upper()

        # Add timestamp for uniqueness
        timestamp = str(int(time.time()))[-8:]  # Last 8 digits

        plant_uid = f"PLT_{org_hash}_{plant_hash}_{timestamp}"

        # Ensure uniqueness (very rare collision case)
        session = self.get_session()
        try:
            existing = session.query(PowerPlantRegistry).filter_by(plant_uid=plant_uid).first()
            if existing:
                # Add random suffix if collision occurs
                import random
                suffix = str(random.randint(1000, 9999))
                plant_uid = f"{plant_uid}_{suffix}"

            return plant_uid
        finally:
            session.close()

    def check_plant_exists(self, plant_name: str, country: str = None) -> Optional[Dict]:
        """
        Check if plant exists in database
        
        Args:
            plant_name: Name of the power plant
            country: Country name (optional for more specific search)
            
        Returns:
            Plant information dict if found, None otherwise
        """
        session = self.get_session()
        try:
            # Clean the plant name to handle whitespace and formatting issues
            clean_plant_name = plant_name.strip() if plant_name else ""
            
            query = session.query(PowerPlantRegistry).filter(
                PowerPlantRegistry.plant_name.ilike(f"%{clean_plant_name}%")
            )
            
            if country:
                query = query.filter(PowerPlantRegistry.country.ilike(f"%{country}%"))
            
            plant = query.first()
            
            if plant:
                return {
                    "id": plant.id,
                    "org_name": plant.org_name,
                    "plant_name": plant.plant_name,
                    "country": plant.country,
                    "org_uid": plant.org_uid,
                    "plant_uid": plant.plant_uid,  # NEW: Include plant UID
                    "plant_status": plant.plant_status.value,
                    "discovery_status": plant.discovery_status.value,
                    "created_at": plant.created_at,
                }
            
            return None
            
        except Exception as e:
            print(f"❌ Error checking plant existence: {e}")
            return None
        finally:
            session.close()
    
    def save_organization_plants(
        self, 
        org_name: str, 
        country: str, 
        plants_list: List[Dict], 
        org_uid: str,
        discovery_session_id: str,
        discovered_from_plant: str
    ) -> bool:
        """
        Save all plants from an organization to database
        
        Args:
            org_name: Organization name
            country: Country name
            plants_list: List of plant dictionaries
            org_uid: Organization unique ID
            discovery_session_id: Session ID that triggered discovery
            discovered_from_plant: Plant name that triggered the discovery
            
        Returns:
            True if successful, False otherwise
        """
        session = self.get_session()
        try:
            plants_saved = 0
            
            for plant_info in plants_list:
                plant_name = plant_info.get("name", "").strip()
                if not plant_name:
                    continue
                
                # Check if plant already exists
                existing = session.query(PowerPlantRegistry).filter_by(
                    plant_name=plant_name,
                    org_name=org_name
                ).first()
                
                if existing:
                    # Update existing record
                    existing.org_uid = org_uid
                    existing.discovery_status = DiscoveryStatus.COMPLETE
                    existing.updated_at = datetime.utcnow()

                    # Generate plant UID if missing
                    if not existing.plant_uid:
                        existing.plant_uid = self.generate_plant_uid(plant_name, org_uid)
                        print(f"   🔧 Generated plant UID for existing plant: {existing.plant_uid}")
                else:
                    # Generate plant UID for new plant
                    plant_uid = self.generate_plant_uid(plant_name, org_uid)

                    # CRITICAL FIX: Normalize plant status to handle case variations
                    raw_status = plant_info.get("status", "operational")
                    normalized_status = self._normalize_plant_status(raw_status)

                    # Create new record
                    plant_record = PowerPlantRegistry(
                        org_name=org_name,
                        plant_name=plant_name,
                        country=country,
                        org_uid=org_uid,
                        plant_uid=plant_uid,  # NEW: Include plant UID
                        plant_status=normalized_status,  # Use normalized status
                        discovery_status=DiscoveryStatus.COMPLETE,
                        discovery_session_id=discovery_session_id,
                        discovered_from_plant=discovered_from_plant
                    )
                    session.add(plant_record)
                    print(f"   🔧 Generated plant UID for new plant: {plant_uid}")
                
                plants_saved += 1
            
            session.commit()
            print(f"✅ Saved {plants_saved} plants for organization: {org_name}")
            return True
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error saving organization plants: {e}")
            return False
        finally:
            session.close()
    
    def get_plants_by_organization(self, org_name: str) -> List[Dict]:
        """
        Get all plants for a given organization
        
        Args:
            org_name: Organization name
            
        Returns:
            List of plant information dictionaries
        """
        session = self.get_session()
        try:
            plants = session.query(PowerPlantRegistry).filter_by(org_name=org_name).all()
            
            return [
                {
                    "id": plant.id,
                    "org_name": plant.org_name,
                    "plant_name": plant.plant_name,
                    "country": plant.country,
                    "org_uid": plant.org_uid,
                    "plant_uid": plant.plant_uid,  # NEW: Include plant UID
                    "plant_status": plant.plant_status.value,
                    "discovery_status": plant.discovery_status.value,
                    "created_at": plant.created_at,
                }
                for plant in plants
            ]
            
        except Exception as e:
            print(f"❌ Error getting plants by organization: {e}")
            return []
        finally:
            session.close()

    def get_plants_by_organization_uid(self, org_uid: str) -> List[Dict]:
        """
        Get all plants for a given organization UID

        Args:
            org_uid: Organization unique identifier

        Returns:
            List of plant information dictionaries
        """
        session = self.get_session()
        try:
            plants = session.query(PowerPlantRegistry).filter_by(org_uid=org_uid).all()

            return [
                {
                    "id": plant.id,
                    "org_name": plant.org_name,
                    "plant_name": plant.plant_name,
                    "country": plant.country,
                    "org_uid": plant.org_uid,
                    "plant_uid": plant.plant_uid,  # NEW: Include plant UID
                    "plant_status": plant.plant_status.value,
                    "discovery_status": plant.discovery_status.value,
                    "created_at": plant.created_at,
                }
                for plant in plants
            ]
        except Exception as e:
            print(f"❌ Error getting plants by org_uid: {e}")
            return []
        finally:
            session.close()

    def generate_plant_uids_for_existing_plants(self) -> bool:
        """
        Generate plant UIDs for existing plants that don't have them

        Returns:
            True if successful, False otherwise
        """
        session = self.get_session()
        try:
            # Get all plants without plant_uid
            plants_without_uid = session.query(PowerPlantRegistry).filter(
                PowerPlantRegistry.plant_uid.is_(None)
            ).all()

            if not plants_without_uid:
                print("✅ All plants already have plant UIDs")
                return True

            print(f"🔧 Generating plant UIDs for {len(plants_without_uid)} plants...")

            updated_count = 0
            for plant in plants_without_uid:
                try:
                    # Generate plant UID
                    plant_uid = self.generate_plant_uid(plant.plant_name, plant.org_uid)

                    # Update the plant record
                    plant.plant_uid = plant_uid
                    plant.updated_at = datetime.utcnow()

                    updated_count += 1
                    print(f"   ✅ {plant.plant_name} → {plant_uid}")

                except Exception as e:
                    print(f"   ❌ Failed to generate UID for {plant.plant_name}: {e}")
                    continue

            session.commit()
            print(f"✅ Generated plant UIDs for {updated_count} plants")
            return True

        except Exception as e:
            session.rollback()
            print(f"❌ Error generating plant UIDs: {e}")
            return False
        finally:
            session.close()

    def get_plant_by_uid(self, plant_uid: str) -> Optional[Dict]:
        """
        Get plant information by plant UID

        Args:
            plant_uid: Plant unique identifier

        Returns:
            Plant information dict if found, None otherwise
        """
        session = self.get_session()
        try:
            plant = session.query(PowerPlantRegistry).filter_by(plant_uid=plant_uid).first()

            if plant:
                return {
                    "id": plant.id,
                    "org_name": plant.org_name,
                    "plant_name": plant.plant_name,
                    "country": plant.country,
                    "org_uid": plant.org_uid,
                    "plant_uid": plant.plant_uid,
                    "plant_status": plant.plant_status.value,
                    "discovery_status": plant.discovery_status.value,
                    "created_at": plant.created_at,
                }

            return None

        except Exception as e:
            print(f"❌ Error getting plant by UID: {e}")
            return None
        finally:
            session.close()

    def test_connection(self) -> bool:
        """
        Test database connection
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            session = self.get_session()
            session.execute(text("SELECT 1"))
            session.close()
            print(f"✅ Database connection successful ({self.db_type})")
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False

    # ===== ENTITY-LEVEL EXTRACTION METHODS =====

    def create_entity_extraction_job(self, org_name: str, org_uid: str, input_plant_name: str,
                                   total_plants: int) -> str:
        """
        Create a new entity-level extraction job

        Args:
            org_name: Organization name
            org_uid: Organization UID
            input_plant_name: Plant that triggered the entity extraction
            total_plants: Total number of plants to process

        Returns:
            Job ID for the created job
        """
        import uuid
        job_id = f"entity_{uuid.uuid4().hex[:8]}"

        session = self.get_session()
        try:
            job = EntityExtractionJob(
                job_id=job_id,
                org_name=org_name,
                org_uid=org_uid,
                input_plant_name=input_plant_name,
                total_plants=total_plants,
                status=EntityJobStatus.PENDING
            )

            session.add(job)
            session.commit()

            print(f"✅ Created entity extraction job: {job_id}")
            print(f"   Organization: {org_name}")
            print(f"   Total plants: {total_plants}")

            return job_id

        except Exception as e:
            session.rollback()
            print(f"❌ Failed to create entity extraction job: {e}")
            raise
        finally:
            session.close()

    def get_plants_for_entity_extraction(self, org_uid: str) -> List[Dict]:
        """
        Get all operational plants for an organization for entity extraction

        Args:
            org_uid: Organization UID

        Returns:
            List of plant dictionaries
        """
        session = self.get_session()
        try:
            plants = session.query(PowerPlantRegistry).filter(
                PowerPlantRegistry.org_uid == org_uid,
                PowerPlantRegistry.plant_status == PlantStatus.OPERATIONAL
            ).all()

            plant_list = []
            for plant in plants:
                plant_list.append({
                    "plant_name": plant.plant_name,
                    "plant_uid": plant.plant_uid,
                    "org_name": plant.org_name,
                    "country": plant.country,
                    "status": plant.plant_status.value
                })

            print(f"📋 Found {len(plant_list)} operational plants for org_uid: {org_uid}")
            return plant_list

        except Exception as e:
            print(f"❌ Failed to get plants for entity extraction: {e}")
            return []
        finally:
            session.close()

    def update_entity_job_status(self, job_id: str, status: EntityJobStatus,
                                completed_plants: int = None, failed_plants: int = None,
                                error_log: str = None) -> bool:
        """
        Update entity extraction job status

        Args:
            job_id: Job ID
            status: New status
            completed_plants: Number of completed plants
            failed_plants: Number of failed plants
            error_log: Error message if any

        Returns:
            True if successful, False otherwise
        """
        session = self.get_session()
        try:
            job = session.query(EntityExtractionJob).filter_by(job_id=job_id).first()

            if not job:
                print(f"❌ Entity job not found: {job_id}")
                return False

            job.status = status
            if completed_plants is not None:
                job.completed_plants = completed_plants
            if failed_plants is not None:
                job.failed_plants = failed_plants
            if error_log:
                job.error_log = error_log
            if status == EntityJobStatus.COMPLETED:
                job.completed_at = datetime.utcnow()

            session.commit()
            print(f"✅ Updated entity job {job_id}: {status.value}")
            return True

        except Exception as e:
            session.rollback()
            print(f"❌ Failed to update entity job status: {e}")
            return False
        finally:
            session.close()

# Global database manager instance
db_manager = None

def get_database_manager() -> DatabaseManager:
    """
    Get global database manager instance (singleton pattern)
    
    Returns:
        DatabaseManager instance
    """
    global db_manager
    if db_manager is None:
        db_manager = DatabaseManager()
    return db_manager