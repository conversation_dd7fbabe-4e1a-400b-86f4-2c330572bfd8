"""
FastAPI Application for Power Plant Extraction Pipeline

This FastAPI application provides HTTP endpoints for the power plant extraction pipeline
without modifying the existing LangGraph implementation. It acts as a wrapper layer
that provides async job management and status tracking.
"""

import asyncio
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import FastAPI, BackgroundTasks, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import json

# Import existing pipeline components - NO CHANGES TO PIPELINE
from agent.graph import graph
from agent.database_manager import get_database_manager
from agent.entity_extraction_controller import EntityExtractionController
from langchain_core.messages import HumanMessage

# FastAPI app initialization
app = FastAPI(
    title="Power Plant Extraction API",
    description="API for power plant data extraction and entity-level processing",
    version="1.0.0"
)

# CORS middleware for web frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===== REQUEST/RESPONSE MODELS =====

class SinglePlantRequest(BaseModel):
    plant_name: str = Field(..., description="Name of the power plant to extract")
    session_id: Optional[str] = Field(None, description="Optional session ID")
    extraction_levels: List[str] = Field(
        default=["organization", "plant", "unit", "transition"],
        description="Levels of extraction to perform"
    )

class EntityExtractionRequest(BaseModel):
    input_plant_name: str = Field(..., description="Plant name that triggers entity extraction")
    batch_size: Optional[int] = Field(default=3, description="Number of plants to process concurrently")
    delay_between_batches: Optional[int] = Field(default=60, description="Delay between batches in seconds")

class AGIPlantRequest(BaseModel):
    plant_name: str = Field(..., description="Name of the power plant to extract")
    entity_id: str = Field(..., description="Organization UID provided by AGI Layer (as entity_id)")
    extraction_levels: List[str] = Field(
        default=["organization", "plant", "unit", "transition"],
        description="Levels of extraction to perform"
    )

class ExtractionStatus(BaseModel):
    job_id: str
    status: str  # pending, running, completed, failed
    plant_name: str
    extraction_type: str  # single_plant, entity_level
    progress: Dict[str, Any]
    started_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    results: Optional[Dict[str, Any]] = None

class QuickDiscoveryRequest(BaseModel):
    plant_name: str = Field(..., description="Plant name for organization discovery")

# ===== JOB TRACKING SYSTEM =====

# In-memory job tracking (use Redis/database for production)
active_jobs: Dict[str, ExtractionStatus] = {}

def create_job(job_type: str, plant_name: str) -> str:
    """Create a new extraction job"""
    job_id = f"{job_type}_{uuid.uuid4().hex[:8]}"
    
    active_jobs[job_id] = ExtractionStatus(
        job_id=job_id,
        status="pending",
        plant_name=plant_name,
        extraction_type=job_type,
        progress={},
        started_at=datetime.now()
    )
    
    return job_id

def update_job_status(job_id: str, status: str, progress: Dict = None, 
                     error: str = None, results: Dict = None):
    """Update job status"""
    if job_id in active_jobs:
        active_jobs[job_id].status = status
        if progress:
            active_jobs[job_id].progress.update(progress)
        if error:
            active_jobs[job_id].error_message = error
        if results:
            active_jobs[job_id].results = results
        if status in ["completed", "failed"]:
            active_jobs[job_id].completed_at = datetime.now()

# ===== BACKGROUND PROCESSING FUNCTIONS =====

async def process_single_plant_extraction(job_id: str, plant_name: str, extraction_levels: List[str]):
    """
    Background task for single plant extraction
    Uses EXISTING pipeline without any modifications
    """
    try:
        update_job_status(job_id, "running", {"phase": "initializing"})
        
        # Create state exactly like existing implementation
        state = {
            "messages": [HumanMessage(content=plant_name)],
            "session_id": job_id,
            "search_phase": 1,  # Start from organization level
            "research_loop_count": 0,
            "web_research_result": [],
            "search_query": [],
            "sources_gathered": [],
            "org_level_complete": False,
            "continue_research": False,
            "phase_complete": False,
            "initial_search_query_count": 5,
            "max_research_loops": 3,
            "reasoning_model": "",
            "extraction_levels": extraction_levels
        }
        
        update_job_status(job_id, "running", {"phase": "executing_pipeline"})
        
        # Execute existing graph - ZERO changes to pipeline
        result = await asyncio.to_thread(graph.invoke, state)
        
        update_job_status(job_id, "completed", 
                         {"phase": "completed"}, 
                         results={"extraction_result": result})
        
        print(f"✅ Job {job_id} completed successfully")
        
    except Exception as e:
        error_msg = f"Single plant extraction failed: {str(e)}"
        update_job_status(job_id, "failed", error=error_msg)
        print(f"❌ Job {job_id} failed: {error_msg}")

async def process_entity_level_extraction(job_id: str, input_plant_name: str,
                                        batch_size: int, delay_between_batches: int):
    """
    Background task for entity-level extraction
    Uses EXISTING EntityExtractionController without modifications
    """
    try:
        update_job_status(job_id, "running", {"phase": "initializing_entity_extraction"})

        # Use existing EntityExtractionController - NO CHANGES
        from agent.entity_extraction_controller import EntityExtractionConfig

        config = EntityExtractionConfig(
            batch_size=batch_size,
            delay_between_batches=delay_between_batches
        )

        controller = EntityExtractionController(input_plant_name, config)

        update_job_status(job_id, "running", {"phase": "executing_entity_extraction"})

        # Execute existing entity extraction - ZERO changes
        result = await controller.execute_entity_extraction()

        update_job_status(job_id, "completed",
                         {"phase": "completed"},
                         results=result)

        print(f"✅ Entity job {job_id} completed successfully")

    except Exception as e:
        error_msg = f"Entity extraction failed: {str(e)}"
        update_job_status(job_id, "failed", error=error_msg)
        print(f"❌ Entity job {job_id} failed: {error_msg}")

async def process_agi_plant_extraction(job_id: str, plant_name: str, entity_id: str, extraction_levels: List[str]):
    """
    Background task for AGI plant extraction
    Uses EXISTING pipeline with AGI-provided Org UID
    """
    try:
        update_job_status(job_id, "running", {"phase": "initializing_agi_extraction"})

        # Create state with AGI Entity ID (Org UID) - MINIMAL CHANGE
        state = {
            "messages": [HumanMessage(content=plant_name)],
            "session_id": job_id,
            "search_phase": 1,  # Start from organization level
            "research_loop_count": 0,
            "web_research_result": [],
            "search_query": [],
            "sources_gathered": [],
            "org_level_complete": False,
            "continue_research": False,
            "phase_complete": False,
            "initial_search_query_count": 5,
            "max_research_loops": 3,
            "reasoning_model": "",
            "extraction_levels": extraction_levels,
            # SIMPLE: Just pass entity_id directly
            "entity_id": entity_id,  # AGI-provided Entity ID (will become org_uid)
        }

        print(f"🔧 SIMPLE API STATE CREATION:")
        print(f"   Plant Name: {plant_name}")
        print(f"   Entity ID: {entity_id}")
        print(f"   Will be saved as org_uid directly")

        update_job_status(job_id, "running", {"phase": "executing_agi_pipeline"})

        # Execute existing graph with AGI context - ZERO changes to graph logic
        result = await asyncio.to_thread(graph.invoke, state)

        # Check if entity extraction was triggered
        if result.get("entity_extraction_triggered"):
            print(f"🔧 Entity extraction triggered - passing AGI context")
            # The entity extraction will be handled by the graph with AGI context

        update_job_status(job_id, "completed",
                         {"phase": "completed"},
                         results={"extraction_result": result})

        print(f"✅ AGI job {job_id} completed successfully with Entity ID: {entity_id}")

    except Exception as e:
        error_msg = f"AGI plant extraction failed: {str(e)}"
        update_job_status(job_id, "failed", error=error_msg)
        print(f"❌ AGI job {job_id} failed: {error_msg}")

# ===== API ENDPOINTS =====

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Power Plant Extraction API",
        "status": "operational",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Comprehensive health check"""
    try:
        # Test database connectivity
        db_manager = get_database_manager()
        db_status = db_manager.test_connection()
        
        return {
            "status": "healthy",
            "database": "connected" if db_status else "disconnected",
            "active_jobs": len(active_jobs),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.post("/api/v1/extraction/single-plant")
async def start_single_plant_extraction(request: SinglePlantRequest, background_tasks: BackgroundTasks):
    """
    Start single plant extraction
    Returns immediately with job_id for status tracking
    """
    try:
        # Create job
        job_id = create_job("single_plant", request.plant_name)
        
        # Start background processing using EXISTING pipeline
        background_tasks.add_task(
            process_single_plant_extraction,
            job_id, 
            request.plant_name, 
            request.extraction_levels
        )
        
        return {
            "job_id": job_id,
            "status": "started",
            "plant_name": request.plant_name,
            "message": "Single plant extraction started",
            "status_url": f"/api/v1/status/{job_id}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start extraction: {str(e)}")

@app.post("/api/v1/extraction/entity-level")
async def start_entity_extraction(request: EntityExtractionRequest, background_tasks: BackgroundTasks):
    """
    Start entity-level extraction for all plants in organization
    Returns immediately with job_id for status tracking
    """
    try:
        # Create job
        job_id = create_job("entity_level", request.input_plant_name)

        # Start background processing using EXISTING controller
        background_tasks.add_task(
            process_entity_level_extraction,
            job_id,
            request.input_plant_name,
            request.batch_size,
            request.delay_between_batches
        )

        return {
            "job_id": job_id,
            "status": "started",
            "input_plant": request.input_plant_name,
            "batch_size": request.batch_size,
            "message": "Entity-level extraction started",
            "status_url": f"/api/v1/status/{job_id}"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start entity extraction: {str(e)}")

@app.post("/api/v1/extraction/agi-plant")
async def start_agi_plant_extraction(request: AGIPlantRequest, background_tasks: BackgroundTasks):
    """
    Start AGI plant extraction with AGI-provided Entity ID (Org UID)
    Returns immediately with job_id for status tracking
    """
    try:
        # Create job
        job_id = create_job("agi_plant", request.plant_name)

        # Start background processing using EXISTING pipeline with AGI Entity ID
        background_tasks.add_task(
            process_agi_plant_extraction,
            job_id,
            request.plant_name,
            request.entity_id,
            request.extraction_levels
        )

        return {
            "job_id": job_id,
            "status": "started",
            "plant_name": request.plant_name,
            "entity_id": request.entity_id,
            "message": "AGI plant extraction started",
            "integration": "AGI Layer",
            "status_url": f"/api/v1/status/{job_id}"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start AGI plant extraction: {str(e)}")

@app.get("/api/v1/status/{job_id}")
async def get_extraction_status(job_id: str):
    """Get extraction job status"""
    if job_id not in active_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return active_jobs[job_id]

@app.get("/api/v1/results/{job_id}")
async def get_extraction_results(job_id: str):
    """Get extraction results"""
    if job_id not in active_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = active_jobs[job_id]
    
    if job.status != "completed":
        raise HTTPException(status_code=400, detail=f"Job not completed. Current status: {job.status}")
    
    return {
        "job_id": job_id,
        "status": job.status,
        "results": job.results,
        "completed_at": job.completed_at
    }

@app.post("/api/v1/discovery/quick-org")
async def quick_organization_discovery(request: QuickDiscoveryRequest):
    """
    Quick organization discovery using EXISTING implementation
    """
    try:
        from agent.quick_org_discovery import perform_quick_org_discovery
        from agent.registry_nodes import get_web_search_function
        
        # Use existing discovery function - NO CHANGES
        web_search_fn = get_web_search_function()
        result = perform_quick_org_discovery(request.plant_name, web_search_fn)
        
        return {
            "plant_name": request.plant_name,
            "organization": result.get("org_name"),
            "country": result.get("country"),
            "plants_found": len(result.get("plants", [])),
            "plants": result.get("plants", []),
            "discovery_result": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Discovery failed: {str(e)}")

@app.get("/api/v1/jobs")
async def list_active_jobs():
    """List all active jobs"""
    return {
        "active_jobs": len(active_jobs),
        "jobs": [
            {
                "job_id": job.job_id,
                "status": job.status,
                "plant_name": job.plant_name,
                "extraction_type": job.extraction_type,
                "started_at": job.started_at
            }
            for job in active_jobs.values()
        ]
    }

# ===== WEBSOCKET FOR REAL-TIME UPDATES =====

@app.websocket("/ws/status/{job_id}")
async def websocket_job_status(websocket: WebSocket, job_id: str):
    """WebSocket endpoint for real-time job status updates"""
    await websocket.accept()
    
    try:
        while True:
            if job_id in active_jobs:
                job_status = active_jobs[job_id]
                await websocket.send_json({
                    "job_id": job_id,
                    "status": job_status.status,
                    "progress": job_status.progress,
                    "timestamp": datetime.now().isoformat()
                })
                
                # Close connection if job is completed
                if job_status.status in ["completed", "failed"]:
                    break
            else:
                await websocket.send_json({
                    "error": "Job not found",
                    "job_id": job_id
                })
                break
            
            await asyncio.sleep(5)  # Update every 5 seconds
            
    except WebSocketDisconnect:
        print(f"WebSocket disconnected for job {job_id}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
