#!/usr/bin/env python3
"""
Test Simple AGI Implementation

This script tests the new simple AGI implementation.
"""

import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_simple_agi_uid_saver():
    """Test the simple AGI UID saver node"""
    print("🔧 Testing Simple AGI UID Saver...")
    
    try:
        from langchain_core.messages import HumanMessage
        from agent.graph import save_agi_org_uid_directly
        
        # Create test state with entity_id
        test_state = {
            "messages": [HumanMessage(content="AKALTARA TPP")],
            "session_id": "test_simple_agi",
            "entity_id": "AGI_ORG_IN_JSW_12345",
        }
        
        print(f"🔧 Input entity_id: {test_state.get('entity_id')}")
        
        # Call the simple AGI UID saver
        result = save_agi_org_uid_directly(test_state)
        
        print(f"🔧 Result org_uid: {result.get('org_uid')}")
        print(f"🔧 Result agi_uid_saved: {result.get('agi_uid_saved')}")
        
        if result.get("org_uid") == "AGI_ORG_IN_JSW_12345" and result.get("agi_uid_saved") == True:
            print("✅ Simple AGI UID saver works correctly")
            return True
        else:
            print("❌ Simple AGI UID saver failed")
            return False
            
    except Exception as e:
        print(f"❌ Simple AGI UID saver test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_generate_uid():
    """Test the simplified generate_uid_node"""
    print("\n🔧 Testing Simple Generate UID Node...")
    
    try:
        from agent.registry_nodes import generate_uid_node
        
        # Create test state with org_uid already set
        test_state = {
            "session_id": "test_simple_uid",
            "org_name": "JSW Energy",
            "plant_country": "India",
            "org_uid": "AGI_ORG_IN_JSW_12345",  # Already set by AGI saver
        }
        
        print(f"🔧 Input org_uid: {test_state.get('org_uid')}")
        
        # Call the simplified generate_uid_node
        result = generate_uid_node(test_state)
        
        print(f"🔧 Result org_uid: {result.get('org_uid')}")
        print(f"🔧 Result uid_generation_complete: {result.get('uid_generation_complete')}")
        
        if result.get("org_uid") == "AGI_ORG_IN_JSW_12345" and result.get("uid_generation_complete") == True:
            print("✅ Simple generate UID node works correctly")
            return True
        else:
            print("❌ Simple generate UID node failed")
            return False
            
    except Exception as e:
        print(f"❌ Simple generate UID node test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_state_creation():
    """Test the simplified API state creation"""
    print("\n🔧 Testing Simple API State Creation...")
    
    try:
        from langchain_core.messages import HumanMessage
        
        # Simulate API state creation
        plant_name = "AKALTARA TPP"
        entity_id = "AGI_ORG_IN_JSW_12345"
        job_id = "test_simple_api"
        extraction_levels = ["organization", "plant", "unit", "transition"]
        
        # Create state like the simplified API does
        state = {
            "messages": [HumanMessage(content=plant_name)],
            "session_id": job_id,
            "search_phase": 1,
            "research_loop_count": 0,
            "web_research_result": [],
            "search_query": [],
            "sources_gathered": [],
            "org_level_complete": False,
            "continue_research": False,
            "phase_complete": False,
            "initial_search_query_count": 5,
            "max_research_loops": 3,
            "reasoning_model": "",
            "extraction_levels": extraction_levels,
            # SIMPLE: Just pass entity_id directly
            "entity_id": entity_id,
        }
        
        print(f"🔧 API state entity_id: {state.get('entity_id')}")
        
        if state.get("entity_id") == entity_id:
            print("✅ Simple API state creation works correctly")
            return True
        else:
            print("❌ Simple API state creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Simple API state creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all simple AGI tests"""
    print("🚀 SIMPLE AGI IMPLEMENTATION TESTING")
    print("=" * 50)
    
    tests = [
        ("Simple AGI UID Saver", test_simple_agi_uid_saver),
        ("Simple Generate UID Node", test_simple_generate_uid),
        ("Simple API State Creation", test_api_state_creation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📋 SIMPLE AGI IMPLEMENTATION TEST SUMMARY:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🚀 SIMPLE AGI IMPLEMENTATION READY!")
        print("\n📋 SIMPLE FLOW:")
        print("1. AGI sends: {plant_name, entity_id}")
        print("2. save_agi_org_uid_directly: entity_id → org_uid")
        print("3. quick_org_discovery: Find plants")
        print("4. generate_uid: Use existing org_uid (no generation)")
        print("5. Continue pipeline with AGI UID")
        print("\n🎯 NO COMPLEX CONTEXT PASSING NEEDED!")
    else:
        print("⚠️ Some tests failed - check simple implementation")

if __name__ == "__main__":
    main()
